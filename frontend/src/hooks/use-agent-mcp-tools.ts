import { useMemo } from 'react';
import { useAgent } from '@/hooks/react-query/agents/use-agents';
import { useDefaultAgentMCPs } from '@/hooks/react-query/agents/use-agents';

export interface MCPTool {
  id: string;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  type: 'configured_mcp' | 'custom_mcp' | 'composio_mcp';
  config: Record<string, any>;
  enabledTools?: string[];
  toolCount?: number;
  qualifiedName?: string;
  isComposio?: boolean;
}

interface AgentMCPToolsResult {
  currentAgentMCPs: MCPTool[];
  defaultAgentMCPs: MCPTool[];
  allAgentMCPs: MCPTool[];
  isLoading: boolean;
  error: any;
}

/**
 * Hook to fetch MCP tools from current agent and default agent
 * Focuses only on custom_mcps and configured_mcps columns
 */
export function useAgentMCPTools(agentId?: string): AgentMCPToolsResult {
  const { data: agent, isLoading: agentLoading, error: agentError } = useAgent(agentId || '');
  const { data: defaultAgentMCPs, isLoading: defaultLoading } = useDefaultAgentMCPs();

  const currentAgentMCPs = useMemo((): MCPTool[] => {
    if (!agent) return [];

    const mcpTools: MCPTool[] = [];

    // Add configured MCPs (standard Smithery MCPs)
    if (agent.configured_mcps) {
      agent.configured_mcps.forEach((mcp) => {
        mcpTools.push({
          id: `configured_mcp_${mcp.name}`,
          name: mcp.name,
          displayName: mcp.name,
          description: `MCP server: ${mcp.name}`,
          icon: getMCPIcon(mcp.name, false),
          type: 'configured_mcp',
          config: mcp.config,
          enabledTools: mcp.enabledTools,
          qualifiedName: mcp.qualifiedName,
        });
      });
    }

    // Add custom MCPs (including Composio integrations)
    if (agent.custom_mcps) {
      agent.custom_mcps.forEach((mcp) => {
        const isComposio = mcp.config?.url?.includes('mcp.composio.dev');
        mcpTools.push({
          id: `custom_mcp_${mcp.name}`,
          name: mcp.name,
          displayName: mcp.name,
          description: isComposio ? `Composio integration: ${mcp.name}` : `Custom MCP server: ${mcp.name}`,
          icon: getMCPIcon(mcp.name, true),
          type: isComposio ? 'composio_mcp' : 'custom_mcp',
          config: mcp.config,
          enabledTools: mcp.enabledTools,
          toolCount: mcp.enabledTools?.length || 0,
          isComposio,
        });
      });
    }

    return mcpTools;
  }, [agent]);

  const defaultAgentMCPTools = useMemo((): MCPTool[] => {
    if (!defaultAgentMCPs) return [];

    const mcpTools: MCPTool[] = [];

    // Add configured MCPs from default agent
    if (defaultAgentMCPs.configured_mcps) {
      defaultAgentMCPs.configured_mcps.forEach((mcp) => {
        mcpTools.push({
          id: `default_configured_mcp_${mcp.name}`,
          name: mcp.name,
          displayName: mcp.name,
          description: `Default MCP server: ${mcp.name}`,
          icon: getMCPIcon(mcp.name, false),
          type: 'configured_mcp',
          config: mcp.config,
          enabledTools: mcp.enabledTools,
          qualifiedName: mcp.qualifiedName,
        });
      });
    }

    // Add custom MCPs from default agent
    if (defaultAgentMCPs.custom_mcps) {
      defaultAgentMCPs.custom_mcps.forEach((mcp) => {
        const isComposio = mcp.config?.url?.includes('mcp.composio.dev');
        mcpTools.push({
          id: `default_custom_mcp_${mcp.name}`,
          name: mcp.name,
          displayName: mcp.name,
          description: isComposio ? `Default Composio integration: ${mcp.name}` : `Default custom MCP: ${mcp.name}`,
          icon: getMCPIcon(mcp.name, true),
          type: isComposio ? 'composio_mcp' : 'custom_mcp',
          config: mcp.config,
          enabledTools: mcp.enabledTools,
          toolCount: mcp.enabledTools?.length || 0,
          isComposio,
        });
      });
    }

    return mcpTools;
  }, [defaultAgentMCPs]);

  const allAgentMCPs = useMemo(() => {
    return [...currentAgentMCPs, ...defaultAgentMCPTools];
  }, [currentAgentMCPs, defaultAgentMCPTools]);

  return {
    currentAgentMCPs,
    defaultAgentMCPs: defaultAgentMCPTools,
    allAgentMCPs,
    isLoading: agentLoading || defaultLoading,
    error: agentError,
  };
}

/**
 * Helper function to get MCP icon based on server name
 * Reuses the same logic from existing components
 */
function getMCPIcon(mcpName: string, isCustom: boolean = false): string {
  if (isCustom) {
    // For custom MCPs (like Composio integrations), try to get specific icons
    const lowerName = mcpName.toLowerCase();
    if (lowerName.includes('gmail') || lowerName.includes('google')) return '📧';
    if (lowerName.includes('slack')) return '💬';
    if (lowerName.includes('github')) return '🐙';
    if (lowerName.includes('notion')) return '📝';
    if (lowerName.includes('calendar')) return '📅';
    if (lowerName.includes('drive')) return '💾';
    if (lowerName.includes('exa')) return '🔍';
    if (lowerName.includes('linear')) return '📋';
    if (lowerName.includes('memory')) return '🧠';
    return '🔧'; // Default for custom MCPs
  }

  // For standard MCPs
  const lowerName = mcpName.toLowerCase();
  if (lowerName.includes('exa')) return '🔍';
  if (lowerName.includes('github')) return '🐙';
  if (lowerName.includes('notion')) return '📝';
  if (lowerName.includes('slack')) return '💬';
  if (lowerName.includes('filesystem')) return '📁';
  if (lowerName.includes('linear')) return '📋';
  if (lowerName.includes('memory')) return '🧠';
  return '⚡'; // Default for standard MCPs
}
