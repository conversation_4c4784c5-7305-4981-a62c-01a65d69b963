import { useMemo } from 'react';
import { useAgent } from '@/hooks/react-query/agents/use-agents';
import { DEFAULT_AGENTPRESS_TOOLS, getToolDisplayName } from '@/app/(dashboard)/agents/_data/tools';
import { usePopularMCPServersV2 } from '@/hooks/react-query/mcp/use-mcp-servers';
import { useDefaultAgentMCPs } from '@/hooks/react-query/agents/use-agents';

export interface AgentTool {
  id: string;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  type: 'agentpress' | 'mcp' | 'custom_mcp' | 'available_mcp' | 'composio';
  enabled: boolean;
  status: 'enabled' | 'disabled' | 'available' | 'needs_auth';
  metadata?: {
    serverName?: string;
    toolCount?: number;
    config?: Record<string, any>;
    enabledTools?: string[];
    qualifiedName?: string;
    authRequired?: boolean;
    canImportFromDefault?: boolean;
  };
}

/**
 * Hook to get ALL available tools for a specific agent
 * Shows enabled tools, disabled tools, and available tools that can be added
 */
export function useAgentTools(agentId?: string) {
  const { data: agent, isLoading: agentLoading, error: agentError } = useAgent(agentId || '');
  const { data: defaultAgentMCPs, isLoading: defaultLoading } = useDefaultAgentMCPs();
  const { data: popularMCPs, isLoading: mcpLoading } = usePopularMCPServersV2(1, 50);

  const tools = useMemo((): AgentTool[] => {
    const allTools: AgentTool[] = [];

    // 1. Add ALL AgentPress tools (both enabled and disabled)
    Object.entries(DEFAULT_AGENTPRESS_TOOLS).forEach(([toolName, defaultTool]) => {
      const agentToolConfig = agent?.agentpress_tools?.[toolName];
      const isEnabled = agentToolConfig?.enabled || false;

      allTools.push({
        id: `agentpress_${toolName}`,
        name: toolName,
        displayName: getToolDisplayName(toolName),
        description: agentToolConfig?.description || defaultTool.description,
        icon: defaultTool.icon,
        type: 'agentpress',
        enabled: isEnabled,
        status: isEnabled ? 'enabled' : 'disabled',
      });
    });

    // 2. Add agent's configured MCPs (standard Smithery MCPs)
    if (agent?.configured_mcps) {
      agent.configured_mcps.forEach((mcp) => {
        allTools.push({
          id: `mcp_${mcp.name}`,
          name: mcp.name,
          displayName: mcp.name,
          description: `MCP server: ${mcp.name}`,
          icon: getMCPIcon(mcp.name, false),
          type: 'mcp',
          enabled: true,
          status: 'enabled',
          metadata: {
            serverName: mcp.name,
            config: mcp.config,
          },
        });
      });
    }

    // 3. Add agent's custom MCPs (including Composio integrations)
    if (agent?.custom_mcps) {
      agent.custom_mcps.forEach((mcp) => {
        const isComposio = mcp.config?.url?.includes('mcp.composio.dev');
        allTools.push({
          id: `custom_mcp_${mcp.name}`,
          name: mcp.name,
          displayName: mcp.name,
          description: isComposio ? `Composio integration: ${mcp.name}` : `Custom MCP server: ${mcp.name}`,
          icon: getMCPIcon(mcp.name, true),
          type: isComposio ? 'composio' : 'custom_mcp',
          enabled: true,
          status: 'enabled',
          metadata: {
            serverName: mcp.name,
            toolCount: mcp.enabledTools?.length || 0,
            config: mcp.config,
            enabledTools: mcp.enabledTools,
          },
        });
      });
    }

    // 4. Add available MCPs from default agent that can be imported
    if (defaultAgentMCPs && agent) {
      // Add configured MCPs from default agent that aren't in current agent
      defaultAgentMCPs.configured_mcps?.forEach((defaultMcp) => {
        const alreadyExists = allTools.some(tool =>
          tool.type === 'mcp' && tool.name === defaultMcp.name
        );

        if (!alreadyExists) {
          allTools.push({
            id: `available_mcp_${defaultMcp.name}`,
            name: defaultMcp.name,
            displayName: defaultMcp.name,
            description: `Available MCP server: ${defaultMcp.name}`,
            icon: getMCPIcon(defaultMcp.name, false),
            type: 'available_mcp',
            enabled: false,
            status: 'available',
            metadata: {
              serverName: defaultMcp.name,
              config: defaultMcp.config,
              canImportFromDefault: true,
            },
          });
        }
      });

      // Add custom MCPs from default agent that aren't in current agent
      defaultAgentMCPs.custom_mcps?.forEach((defaultMcp) => {
        const alreadyExists = allTools.some(tool =>
          (tool.type === 'custom_mcp' || tool.type === 'composio') && tool.name === defaultMcp.name
        );

        if (!alreadyExists) {
          const isComposio = defaultMcp.config?.url?.includes('mcp.composio.dev');
          allTools.push({
            id: `available_${isComposio ? 'composio' : 'custom_mcp'}_${defaultMcp.name}`,
            name: defaultMcp.name,
            displayName: defaultMcp.name,
            description: isComposio ? `Available Composio integration: ${defaultMcp.name}` : `Available custom MCP: ${defaultMcp.name}`,
            icon: getMCPIcon(defaultMcp.name, true),
            type: isComposio ? 'composio' : 'custom_mcp',
            enabled: false,
            status: 'available',
            metadata: {
              serverName: defaultMcp.name,
              toolCount: defaultMcp.enabledTools?.length || 0,
              config: defaultMcp.config,
              enabledTools: defaultMcp.enabledTools,
              canImportFromDefault: true,
            },
          });
        }
      });
    }

    // 5. Add popular MCPs that need authentication
    if (popularMCPs?.servers) {
      popularMCPs.servers.forEach((mcpServer) => {
        const alreadyExists = allTools.some(tool =>
          tool.metadata?.qualifiedName === mcpServer.qualifiedName ||
          tool.name.toLowerCase() === mcpServer.displayName.toLowerCase()
        );

        if (!alreadyExists) {
          allTools.push({
            id: `needs_auth_${mcpServer.qualifiedName.replace(/[@\/]/g, '_')}`,
            name: mcpServer.displayName,
            displayName: mcpServer.displayName,
            description: mcpServer.description,
            icon: getMCPIcon(mcpServer.displayName, false),
            type: 'available_mcp',
            enabled: false,
            status: 'needs_auth',
            metadata: {
              serverName: mcpServer.displayName,
              qualifiedName: mcpServer.qualifiedName,
              authRequired: true,
            },
          });
        }
      });
    }

    return allTools;
  }, [agent, defaultAgentMCPs, popularMCPs]);

  const enabledTools = useMemo(() => {
    return tools.filter(tool => tool.enabled);
  }, [tools]);

  const availableTools = useMemo(() => {
    return tools.filter(tool => !tool.enabled);
  }, [tools]);

  return {
    tools,
    enabledTools,
    availableTools,
    isLoading: agentLoading || defaultLoading || mcpLoading,
    error: agentError,
    agent,
  };
}

/**
 * Helper function to get MCP icon based on server name
 * Reuses the same logic from existing components
 */
function getMCPIcon(mcpName: string, isCustom: boolean = false): string {
  if (isCustom) {
    // For custom MCPs (like Composio integrations), try to get specific icons
    const lowerName = mcpName.toLowerCase();
    if (lowerName.includes('gmail') || lowerName.includes('google')) return '📧';
    if (lowerName.includes('slack')) return '💬';
    if (lowerName.includes('github')) return '🐙';
    if (lowerName.includes('notion')) return '📝';
    if (lowerName.includes('calendar')) return '📅';
    if (lowerName.includes('drive')) return '💾';
    if (lowerName.includes('exa')) return '🔍';
    return '🔧'; // Default for custom MCPs
  }

  // For standard MCPs
  const lowerName = mcpName.toLowerCase();
  if (lowerName.includes('exa')) return '🔍';
  if (lowerName.includes('github')) return '🐙';
  if (lowerName.includes('notion')) return '📝';
  if (lowerName.includes('slack')) return '💬';
  if (lowerName.includes('filesystem')) return '📁';
  return '⚡'; // Default for standard MCPs
}
