'use client';

import React, { forwardRef, useCallback, useMemo } from 'react';
import { MentionsInput, Mention } from 'react-mentions';
import { cn } from '@/lib/utils';
import { useMCPToolClassification, ClassifiedMCPTool } from '@/hooks/use-mcp-tool-classification';

interface ToolMentionsInputProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  selectedAgentId?: string;
  isDraggingOver?: boolean;
}

interface MentionData {
  id: string;
  display: string;
  tool: ClassifiedMCPTool;
}

export const ToolMentionsInput = forwardRef<HTMLTextAreaElement, ToolMentionsInputProps>(
  (
    {
      value,
      onChange,
      onKeyDown,
      placeholder = 'Type @ to mention tools...',
      disabled = false,
      className,
      selectedAgentId,
      isDraggingOver = false,
    },
    ref
  ) => {
    const { allTools, isLoading } = useMCPToolClassification(selectedAgentId);

    // Convert ALL MCP tools to mention data format
    const mentionData = useMemo((): MentionData[] => {
      return allTools.map((tool) => ({
        id: tool.id,
        display: tool.displayName,
        tool,
      }));
    }, [allTools]);

    // Handle mention input change
    const handleChange = useCallback(
      (_event: any, newValue: string) => {
        onChange(newValue);
      },
      [onChange]
    );

    // Custom suggestion renderer
    const renderSuggestion = useCallback(
      (suggestion: MentionData, _search: string, highlightedDisplay: React.ReactNode) => {
        const { tool } = suggestion;

        // Get status indicator
        const getStatusIndicator = () => {
          switch (tool.status) {
            case 'connected_to_agent':
              return <div className="w-2 h-2 rounded-full bg-green-500" />;
            case 'connected_to_account':
              return <div className="w-2 h-2 rounded-full bg-blue-500" />;
            case 'available_to_connect':
              return <div className="w-2 h-2 rounded-full bg-orange-500" />;
            default:
              return null;
          }
        };

        const getStatusText = () => {
          switch (tool.status) {
            case 'connected_to_agent':
              return 'Connected';
            case 'connected_to_account':
              return 'Add to Agent';
            case 'available_to_connect':
              return 'Connect';
            default:
              return tool.type;
          }
        };

        return (
          <div className="flex items-center gap-3 p-2 hover:bg-muted/50 cursor-pointer">
            <div className="w-6 h-6 rounded-md bg-primary/10 flex items-center justify-center flex-shrink-0">
              <span className="text-xs">{tool.icon}</span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm text-foreground flex items-center gap-2">
                {highlightedDisplay}
                {getStatusIndicator()}
              </div>
            </div>
            <div className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
              {getStatusText()}
            </div>
          </div>
        );
      },
      []
    );

    // Custom mention display transform
    const displayTransform = useCallback((_id: string, display: string) => {
      return `@${display}`;
    }, []);

    // Simplified styles for the mentions input
    const mentionsInputStyle = {
      control: {
        backgroundColor: 'transparent',
        fontSize: '16px',
        fontWeight: 'normal',
        border: 'none',
        outline: 'none',
      },
      '&multiLine': {
        control: {
          fontFamily: 'inherit',
          minHeight: '40px',
          maxHeight: '200px',
          overflow: 'auto',
          border: 'none',
          outline: 'none',
        },
        highlighter: {
          padding: '8px',
          border: 'none',
          overflow: 'hidden',
        },
        input: {
          padding: '8px',
          border: 'none',
          outline: 'none',
          backgroundColor: 'transparent',
          color: 'inherit',
          resize: 'none',
        },
      },
    };

    // Mention markup pattern
    const mentionMarkup = '@[__display__](__id__)';

    return (
      <div className={cn('w-full relative', className)}>

        <MentionsInput
          value={value}
          onChange={handleChange}
          onKeyDown={onKeyDown}
          placeholder={placeholder}
          disabled={disabled || isLoading}
          className={cn(
            'w-full bg-transparent border-none shadow-none focus-visible:ring-0 px-2 py-1 text-base min-h-[40px] max-h-[200px] overflow-y-auto resize-none',
            isDraggingOver ? 'opacity-40' : '',
            'mentions-input'
          )}
          style={mentionsInputStyle}
          singleLine={false}
          allowSpaceInQuery={true}
          forceSuggestionsAboveCursor={false}
          suggestionsPortalHost={typeof document !== 'undefined' ? document.body : undefined}
          a11ySuggestionsListLabel="Available tools"
        >
          <Mention
            trigger="@"
            data={mentionData}
            renderSuggestion={renderSuggestion}
            displayTransform={displayTransform}
            markup={mentionMarkup}
            appendSpaceOnAdd={true}
            isLoading={isLoading}
            style={{
              backgroundColor: 'hsl(var(--primary))',
              color: 'hsl(var(--primary-foreground))',
              padding: '2px 6px',
              borderRadius: '4px',
              fontWeight: '500',
              fontSize: '14px',
            }}
          />
        </MentionsInput>
      </div>
    );
  }
);

ToolMentionsInput.displayName = 'ToolMentionsInput';
