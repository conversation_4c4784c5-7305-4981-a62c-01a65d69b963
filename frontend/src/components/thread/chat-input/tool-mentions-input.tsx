'use client';

import React, { forwardRef, useCallback, useMemo } from 'react';
import { MentionsInput, Mention } from 'react-mentions';
import { cn } from '@/lib/utils';
import { useMCPToolClassification, ClassifiedMCPTool } from '@/hooks/use-mcp-tool-classification';
import { Badge } from '@/components/ui/badge';
import { Check, ChevronRight, ExternalLink, Zap } from 'lucide-react';

interface ToolMentionsInputProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  selectedAgentId?: string;
  isDraggingOver?: boolean;
}

interface MentionData {
  id: string;
  display: string;
  tool: ClassifiedMCPTool;
}

export const ToolMentionsInput = forwardRef<HTMLTextAreaElement, ToolMentionsInputProps>(
  (
    {
      value,
      onChange,
      onKeyDown,
      placeholder = 'Type @ to mention tools...',
      disabled = false,
      className,
      selectedAgentId,
      isDraggingOver = false,
    },
    ref
  ) => {
    const { allTools, isLoading } = useMCPToolClassification(selectedAgentId);

    // Convert ALL MCP tools to mention data format
    const mentionData = useMemo((): MentionData[] => {
      return allTools.map((tool) => ({
        id: tool.id,
        display: tool.displayName,
        tool,
      }));
    }, [allTools]);

    // Handle mention input change
    const handleChange = useCallback(
      (_event: any, newValue: string) => {
        onChange(newValue);
      },
      [onChange]
    );

    // Render tool icon with proper fallback logic (similar to MCP server card)
    const renderToolIcon = useCallback((tool: ClassifiedMCPTool) => {
      const isIconUrl = tool.icon && tool.icon.startsWith('http');

      return (
        <div className="w-7 h-7 flex-shrink-0 flex items-center justify-center bg-background border border-border rounded-md">
          {isIconUrl ? (
            <img
              src={tool.icon}
              alt={tool.displayName}
              className="w-7 h-7 object-contain rounded-md"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = target.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'flex';
              }}
            />
          ) : null}

          {/* Fallback icon */}
          <div
            className="w-7 h-7 flex items-center justify-center text-muted-foreground"
            style={{ display: isIconUrl ? 'none' : 'flex' }}
          >
            {tool.icon && !isIconUrl ? (
              <span className="text-lg">{tool.icon}</span>
            ) : (
              <Zap className="h-5 w-5" />
            )}
          </div>
        </div>
      );
    }, []);

    // Custom suggestion renderer
    const renderSuggestion = useCallback(
      (suggestion: MentionData, _search: string, highlightedDisplay: React.ReactNode) => {
        const { tool } = suggestion;

        const getStatusConfig = () => {
          switch (tool.status) {
            case 'connected_to_agent':
              return {
                badge: 'Connected',
                badgeVariant: 'default' as const,
                icon: <Check className="h-4 w-4 text-green-500" />
              };
            case 'connected_to_account':
              return {
                badge: 'Add to Agent',
                badgeVariant: 'secondary' as const,
                icon: <ChevronRight className="h-4 w-4 text-blue-500" />
              };
            case 'available_to_connect':
              return {
                badge: 'Connect',
                badgeVariant: 'outline' as const,
                icon: <ExternalLink className="h-4 w-4 text-orange-500" />
              };
            default:
              return {
                badge: tool.type,
                badgeVariant: 'outline' as const,
                icon: null
              };
          }
        };

        const statusConfig = getStatusConfig();

        return (
          <div className="flex items-center gap-3 p-3 hover:bg-muted/50 cursor-pointer transition-colors">
            {/* Tool Icon */}
            {renderToolIcon(tool)}

            {/* Tool Info */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm text-foreground truncate">
                {highlightedDisplay}
              </div>
              {tool.toolCount && (
                <div className="text-xs text-muted-foreground">
                  {tool.toolCount} tools
                </div>
              )}
            </div>

            {/* Status Icon and Badge */}
            <div className="flex items-center gap-2 flex-shrink-0">
              {statusConfig.icon}
              <Badge variant={statusConfig.badgeVariant} className="text-xs">
                {statusConfig.badge}
              </Badge>
            </div>
          </div>
        );
      },
      [renderToolIcon]
    );

    // Custom mention display transform
    const displayTransform = useCallback((_id: string, display: string) => {
      return `@${display}`;
    }, []);

    // Enhanced styles for the mentions input with better dropdown styling
    const mentionsInputStyle = {
      control: {
        backgroundColor: 'transparent',
        fontSize: '16px',
        fontWeight: 'normal',
        border: 'none',
        outline: 'none',
      },
      '&multiLine': {
        control: {
          fontFamily: 'inherit',
          minHeight: '40px',
          maxHeight: '200px',
          overflow: 'auto',
          border: 'none',
          outline: 'none',
        },
        highlighter: {
          padding: '8px',
          border: 'none',
          overflow: 'hidden',
        },
        input: {
          padding: '8px',
          border: 'none',
          outline: 'none',
          backgroundColor: 'transparent',
          color: 'inherit',
          resize: 'none',
        },
      },
      suggestions: {
        list: {
          backgroundColor: 'hsl(var(--background))',
          border: '1px solid hsl(var(--border))',
          borderRadius: '8px',
          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
          fontSize: '14px',
          maxHeight: '320px',
          overflow: 'auto',
          padding: '4px',
          zIndex: 9999,
        },
        item: {
          padding: '0',
          borderRadius: '6px',
          '&focused': {
            backgroundColor: 'hsl(var(--muted))',
          },
        },
      },
    };

    // Mention markup pattern
    const mentionMarkup = '@[__display__](__id__)';

    return (
      <div className={cn('w-full relative', className)}>

        <MentionsInput
          value={value}
          onChange={handleChange}
          onKeyDown={onKeyDown}
          placeholder={placeholder}
          disabled={disabled || isLoading}
          className={cn(
            'w-full bg-transparent border-none shadow-none focus-visible:ring-0 px-2 py-1 text-base min-h-[40px] max-h-[200px] overflow-y-auto resize-none',
            isDraggingOver ? 'opacity-40' : '',
            'mentions-input'
          )}
          style={mentionsInputStyle}
          singleLine={false}
          allowSpaceInQuery={true}
          forceSuggestionsAboveCursor={false}
          suggestionsPortalHost={typeof document !== 'undefined' ? document.body : undefined}
          a11ySuggestionsListLabel="Available tools"
        >
          <Mention
            trigger="@"
            data={mentionData}
            renderSuggestion={renderSuggestion}
            displayTransform={displayTransform}
            markup={mentionMarkup}
            appendSpaceOnAdd={true}
            isLoading={isLoading}
            style={{
              backgroundColor: 'hsl(var(--primary))',
              color: 'hsl(var(--primary-foreground))',
              padding: '2px 6px',
              borderRadius: '4px',
              fontWeight: '500',
              fontSize: '14px',
            }}
          />
        </MentionsInput>
      </div>
    );
  }
);

ToolMentionsInput.displayName = 'ToolMentionsInput';
